import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score
import torch
import torch.nn as nn
import torch.optim as optim

class Autoencoder(nn.Module):
    def __init__(self, input_size):
        super(Autoencoder, self).__init__()
        # 编码器
        self.encoder = nn.Sequential(
            nn.Linear(input_size, 32),
            nn.ReLU(),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 8),
            nn.ReLU()
        )

        # 解码器
        self.decoder = nn.Sequential(
            nn.Linear(8, 16),
            nn.ReLU(),
            nn.Linear(16, 32),
            nn.ReLU(),
            nn.Linear(32, input_size),
            nn.Sigmoid()
        )

    def forward(self, x):
        encoded = self.encoder(x)
        decoded = self.decoder(encoded)
        return decoded

def main():
    # 加载数据
    male_df = pd.read_excel(r'D:\数学建模\2025国赛C题\男胎数据.xlsx')
    female_df = pd.read_excel(r'D:\数学建模\2025国赛C题\女胎数据.xlsx')

    # 选择共同的数值特征
    features = ['GC含量', '13号染色体的Z值', '18号染色体的Z值', '21号染色体的Z值',
                'X染色体的Z值', 'X染色体浓度', '原始读段数']

    # 女胎数据分割：80%训练，20%测试
    female_train, female_test = train_test_split(female_df, test_size=0.2, random_state=42)

    # 构建训练集：全部男胎 + 80%女胎
    train_male = male_df[features].fillna(0)
    train_female = female_train[features].fillna(0)
    train_X = pd.concat([train_male, train_female])
    train_y = np.concatenate([np.ones(len(train_male)), np.zeros(len(train_female))])

    # 构建测试集：20%女胎
    test_X = female_test[features].fillna(0)
    test_y = np.zeros(len(test_X))

    # 标准化
    scaler = StandardScaler()
    train_X = scaler.fit_transform(train_X)
    test_X = scaler.transform(test_X)

    # 转换为张量
    train_X_tensor = torch.FloatTensor(train_X)
    test_X_tensor = torch.FloatTensor(test_X)

    # 训练自编码器模型（只用女胎数据训练，学习正常模式）
    female_train_X = train_X[train_y == 0]  # 只选择女胎数据
    female_train_tensor = torch.FloatTensor(female_train_X)

    model = Autoencoder(train_X_tensor.shape[1])
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    # 训练模型
    for epoch in range(200):
        model.train()
        optimizer.zero_grad()
        outputs = model(female_train_tensor)
        loss = criterion(outputs, female_train_tensor)
        loss.backward()
        optimizer.step()
        if epoch % 20 == 0:
            print(f'Epoch {epoch}, Loss: {loss.item():.4f}')

    # 计算重构误差阈值（使用训练集女胎数据）
    model.eval()
    with torch.no_grad():
        female_reconstructed = model(female_train_tensor)
        female_errors = torch.mean((female_train_tensor - female_reconstructed) ** 2, dim=1)
        threshold = torch.mean(female_errors) + 2 * torch.std(female_errors)
        print(f'异常检测阈值: {threshold:.4f}')

    # 在测试集上进行异常检测
    with torch.no_grad():
        test_reconstructed = model(test_X_tensor)
        test_errors = torch.mean((test_X_tensor - test_reconstructed) ** 2, dim=1)
        test_pred = (test_errors > threshold).float().numpy()

    # 计算准确率
    accuracy = accuracy_score(test_y, test_pred)
    print(f'测试集准确率: {accuracy:.4f}')

    # 显示一些统计信息
    print(f'测试集中预测为异常的样本数: {np.sum(test_pred)}')
    print(f'测试集总样本数: {len(test_pred)}')
    print(f'异常比例: {np.sum(test_pred)/len(test_pred):.4f}')

if __name__ == "__main__":
    main()