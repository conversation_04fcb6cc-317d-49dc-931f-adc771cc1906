import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import torch
import torch.nn as nn
import torch.optim as optim

class MLP(nn.Module):
    def __init__(self, input_size):
        super(MLP, self).__init__()
        self.fc1 = nn.Linear(input_size, 64)
        self.fc2 = nn.Linear(64, 32)
        self.fc3 = nn.Linear(32, 1)

    def forward(self, x):
        x = torch.relu(self.fc1(x))
        x = torch.relu(self.fc2(x))
        x = torch.sigmoid(self.fc3(x))
        return x

def main():
    # 加载数据
    male_df = pd.read_excel(r'D:\数学建模\2025国赛C题\男胎数据.xlsx')
    female_df = pd.read_excel(r'D:\数学建模\2025国赛C题\女胎数据.xlsx')

    # 选择共同的数值特征
    features = ['GC含量', '13号染色体的Z值', '18号染色体的Z值', '21号染色体的Z值',
                'X染色体的Z值', 'X染色体浓度', '原始读段数']

    # 女胎数据分割：80%训练，20%测试
    female_train, female_test = train_test_split(female_df, test_size=0.2, random_state=42)

    # 构建训练集：全部男胎 + 80%女胎
    train_male = male_df[features].fillna(0)
    train_female = female_train[features].fillna(0)
    train_X = pd.concat([train_male, train_female])
    train_y = np.concatenate([np.ones(len(train_male)), np.zeros(len(train_female))])

    # 构建测试集：20%女胎
    test_X = female_test[features].fillna(0)
    test_y = np.zeros(len(test_X))

    # 标准化
    scaler = StandardScaler()
    train_X = scaler.fit_transform(train_X)
    test_X = scaler.transform(test_X)

    # 转换为张量
    train_X = torch.FloatTensor(train_X)
    train_y = torch.FloatTensor(train_y).reshape(-1, 1)
    test_X = torch.FloatTensor(test_X)
    test_y = torch.FloatTensor(test_y).reshape(-1, 1)

    # 训练模型
    model = MLP(train_X.shape[1])
    criterion = nn.BCELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    for epoch in range(100):
        model.train()
        optimizer.zero_grad()
        outputs = model(train_X)
        loss = criterion(outputs, train_y)
        loss.backward()
        optimizer.step()
        if epoch % 10 == 0:
            print(f'Epoch {epoch}, Loss: {loss.item():.4f}')

    # 测试
    model.eval()
    with torch.no_grad():
        test_outputs = model(test_X)
        test_pred = (test_outputs > 0.5).float()
        accuracy = (test_pred == test_y).float().mean()

    print(f'测试集准确率: {accuracy:.4f}')

if __name__ == "__main__":
    main()





