# 第三问：使用支持向量机算法预测Y染色体浓度检测结果
import pandas as pd
import numpy as np
from sklearn.svm import SVC, SVR
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics import classification_report, accuracy_score, r2_score, mean_squared_error
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 支持向量机分类
def svm_classification(df):
    # 数据预处理
    df_clean = df.dropna(subset=['检测孕周', '孕妇BMI', 'Y染色体浓度', '年龄', 'GC含量'])

    # 特征选择
    features = ['检测孕周', '孕妇BMI', '年龄', 'GC含量', '原始读段数']
    X = df_clean[features]
    y = (df_clean['Y染色体浓度'] > 0.04).astype(int)  # 目标变量：是否达标

    # 数据分割和模型训练
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    # 使用网格搜索优化参数
    param_grid = {
        'C': [0.1, 1, 10, 100],
        'gamma': [1, 0.1, 0.01, 0.001],
        'kernel': ['rbf', 'linear']
    }
    grid_search = GridSearchCV(SVC(), param_grid, refit=True, verbose=2)
    grid_search.fit(X_train, y_train)

    # 预测和评估
    y_pred = grid_search.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)

    print("\n支持向量机分类结果：")
    print(f"准确率: {accuracy:.4f}")
    print(classification_report(y_test, y_pred))

    # 特征重要性（SVM没有直接的特征重要性输出，但可以通过系数来分析）
    if grid_search.best_estimator_.kernel == 'linear':
        feature_importance = pd.DataFrame({
            'feature': features,
            'importance': grid_search.best_estimator_.coef_[0]
        }).sort_values('importance', ascending=False)
        print("\n特征重要性：")
        print(feature_importance)

    # 可视化特征重要性
    plt.figure(figsize=(8, 5))
    sns.barplot(data=feature_importance, x='importance', y='feature')
    plt.title('支持向量机分类特征重要性')
    plt.xlabel('重要性')
    plt.tight_layout()
    plt.show()

    return grid_search.best_estimator_

# 支持向量机回归
def svm_regression(df):
    # 数据预处理
    df_clean = df.dropna(subset=['检测孕周', '孕妇BMI', 'Y染色体浓度', '年龄', 'GC含量'])

    # 特征选择
    features = ['检测孕周', '孕妇BMI', '年龄', 'GC含量', '原始读段数']
    X = df_clean[features]
    y = df_clean['Y染色体浓度']

    # 数据分割
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

    # 使用网格搜索优化参数
    param_grid = {
        'C': [0.1, 1, 10, 100],
        'gamma': [1, 0.1, 0.01, 0.001],
        'kernel': ['rbf', 'linear']
    }
    grid_search = GridSearchCV(SVR(), param_grid, refit=True, verbose=2)
    grid_search.fit(X_train, y_train)

    # 预测
    y_pred = grid_search.predict(X_test)

    # 评估
    r2 = r2_score(y_test, y_pred)
    mse = mean_squared_error(y_test, y_pred)
    rmse = np.sqrt(mse)

    print("\n支持向量机回归结果：")
    print(f"R²: {r2:.4f}")
    print(f"RMSE: {rmse:.4f}")

    # 预测vs实际值图
    plt.figure(figsize=(8, 6))
    plt.scatter(y_test, y_pred, alpha=0.6)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], c='r', lw=2)
    plt.xlabel('实际Y染色体浓度')
    plt.ylabel('预测Y染色体浓度')
    plt.title(f'支持向量机回归预测结果 (R² = {r2:.4f})')
    plt.tight_layout()
    plt.show()

    return grid_search.best_estimator_

# BMI分组最佳检测时间
def bmi_group_optimal_time(df):
    # 数据预处理
    df_clean = df.dropna(subset=['孕妇代码', '检测孕周', '孕妇BMI', 'Y染色体浓度', '年龄', 'GC含量'])

    # 定义BMI分组（按照题目要求）
    def get_bmi_group(bmi):
        if 20 <= bmi < 28:
            return '[20,28)'
        elif 28 <= bmi < 32:
            return '[28,32)'
        elif 32 <= bmi < 36:
            return '[32,36)'
        elif 36 <= bmi < 40:
            return '[36,40)'
        elif bmi >= 40:
            return '≥40'
        else:
            return '<20'  # BMI小于20的情况

    df_clean = df_clean.copy()
    df_clean['BMI组'] = df_clean['孕妇BMI'].apply(get_bmi_group)

    # 为每个孕妇找到首次达标时间
    individual_data = []
    for _, group in df_clean.groupby('孕妇代码'):
        group = group.sort_values('检测孕周')
        mask = group['Y染色体浓度'] > 0.04
        if mask.any():
            optimal_time = group[mask]['检测孕周'].iloc[0]
            达标状态 = 1
        else:
            optimal_time = group['检测孕周'].iloc[-1]
            达标状态 = 0

        info = group.iloc[0]
        individual_data.append({
            '孕妇BMI': info['孕妇BMI'],
            'BMI组': info['BMI组'],
            '年龄': info['年龄'],
            'GC含量': info['GC含量'],
            '检测时间': optimal_time,
            '达标状态': 达标状态
        })

    individual_df = pd.DataFrame(individual_data)

    print("\n BMI分组统计：")
    for group_name in individual_df['BMI组'].unique():
        group_data = individual_df[individual_df['BMI组'] == group_name]
        print(f"\n{group_name}组:")
        print(f"  样本数: {len(group_data)}")
        print(f"  达标率: {group_data['达标状态'].mean():.2%}")
        print(f"  平均检测时间: {group_data['检测时间'].mean():.1f}周")

    # 使用支持向量机为每个BMI组找最佳时间
    optimal_times_by_group = {}

    for group_name in individual_df['BMI组'].unique():
        group_data = individual_df[individual_df['BMI组'] == group_name]

        # 特征：年龄、GC含量
        features = ['年龄', 'GC含量']
        X = group_data[features]
        y = group_data['检测时间']

        if len(group_data) > 10:  # 确保有足够样本
            # 支持向量机回归
            svm_reg = SVR(kernel='linear')
            svm_reg.fit(X, y)

            # 预测该组的最佳时间（使用组内平均特征）
            avg_features = X.mean().values.reshape(1, -1)
            predicted_time = svm_reg.predict(avg_features)[0]

            # 考虑达标率调整时间
            success_rate = group_data['达标状态'].mean()
            if success_rate < 0.8:  # 达标率低，建议稍晚检测
                predicted_time += 1
            elif success_rate > 0.95:  # 达标率很高，可以稍早检测
                predicted_time -= 0.5

            optimal_times_by_group[group_name] = {
                '建议检测时间': round(predicted_time, 1),
                '样本数': len(group_data),
                '达标率': f"{success_rate:.2%}",
                '特征重要性': svm_reg.coef_[0]
            }
        else:
            # 样本太少，使用平均值
            optimal_times_by_group[group_name] = {
                '建议检测时间': round(group_data['检测时间'].mean(), 1),
                '样本数': len(group_data),
                '达标率': f"{group_data['达标状态'].mean():.2%}",
                '特征重要性': '样本不足'
            }

    print("\n各BMI组最佳检测时间建议：")
    for group, info in optimal_times_by_group.items():
        print(f"\n{group}:")
        print(f"  建议检测时间: {info['建议检测时间']}周")
        print(f"  基于样本数: {info['样本数']}")
        print(f"  该组达标率: {info['达标率']}")
        if isinstance(info['特征重要性'], np.ndarray):
            print(f"  年龄重要性: {info['特征重要性'][0]:.3f}")
            print(f"  GC含量重要性: {info['特征重要性'][1]:.3f}")

    return optimal_times_by_group, individual_df

def main():
    # 加载数据
    df = pd.read_excel(r'D:\数学建模\2025国赛C题\男胎数据.xlsx')
    print(f"数据形状: {df.shape}")

    # 支持向量机回归任务：预测Y染色体浓度
    svm_reg = svm_regression(df)

    # BMI分组最佳检测时间分析
    optimal_times, _ = bmi_group_optimal_time(df)

    # 总结建议
    print("基于支持向量机的BMI分组最佳检测时间")

if __name__ == "__main__":
    main()