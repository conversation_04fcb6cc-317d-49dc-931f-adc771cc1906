import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import IsolationForest
from sklearn.metrics import accuracy_score

def main():
    # 加载数据
    male_df = pd.read_excel(r'D:\数学建模\2025国赛C题\男胎数据.xlsx')
    female_df = pd.read_excel(r'D:\数学建模\2025国赛C题\女胎数据.xlsx')
    
    # 选择共同的数值特征
    features = ['GC含量', '13号染色体的Z值', '18号染色体的Z值', '21号染色体的Z值', 
                'X染色体的Z值', 'X染色体浓度', '原始读段数']
    
    # 女胎数据分割：80%训练，20%测试
    female_train, female_test = train_test_split(female_df, test_size=0.2, random_state=42)
    
    # 构建训练集：全部男胎 + 80%女胎
    train_male = male_df[features].fillna(0)
    train_female = female_train[features].fillna(0)
    train_X = pd.concat([train_male, train_female])
    train_y = np.concatenate([np.ones(len(train_male)), np.zeros(len(train_female))])
    
    # 构建测试集：20%女胎
    test_X = female_test[features].fillna(0)
    test_y = np.zeros(len(test_X))
    
    # 标准化
    scaler = StandardScaler()
    train_X = scaler.fit_transform(train_X)
    test_X = scaler.transform(test_X)
    
    # 训练Isolation Forest模型
    model = IsolationForest(contamination=0.1, random_state=42)
    model.fit(train_X)
    
    # 预测
    test_pred = model.predict(test_X)
    # Isolation Forest输出：1为正常，-1为异常
    # 转换为二分类：-1(异常)→1(男胎)，1(正常)→0(女胎)
    test_pred_binary = np.where(test_pred == -1, 1, 0)
    
    # 计算准确率
    accuracy = accuracy_score(test_y, test_pred_binary)
    print(f'测试集准确率: {accuracy:.4f}')

if __name__ == "__main__":
    main()
